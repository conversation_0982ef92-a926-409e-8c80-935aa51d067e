#!/usr/bin/env python3
"""
PDF Comb Field Compatibility Script for GhostScript Flattening
Fixes issues with numberOfCells fields losing content when flattened with GhostScript 9.27
"""

import sys
import os
import re
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import pypdf
from pypdf import Pdf<PERSON>riter, PdfReader
from pypdf.generic import DictionaryObject, ArrayObject, IndirectObject, TextStringObject, NameObject, BooleanObject

class CombFieldFixer:
    def __init__(self, input_pdf: str):
        """Initialize with input PDF path"""
        self.input_path = input_pdf
        self.reader = None
        self.writer = None
        self.open_pdf()
        
    def open_pdf(self) -> bool:
        """Open PDF file and prepare for processing"""
        try:
            self.reader = PdfReader(self.input_path)
            self.writer = PdfWriter()
            self.writer.clone_reader_document_root(self.reader)
            return True
        except Exception as e:
            print(f"Error opening PDF: {e}")
            return False
            
    def fix_comb_fields(self) -> bool:
        """Fix comb fields for better GhostScript compatibility"""
        try:
            if '/AcroForm' not in self.writer._root_object:
                print("No AcroForm found in PDF")
                return False
                
            acroform = self.writer._root_object['/AcroForm']
            if '/Fields' not in acroform:
                print("No fields found in AcroForm")
                return False
                
            # Configure AcroForm for better flattening
            self._configure_acroform(acroform)
            
            # Process all fields
            self._process_fields_recursive(acroform['/Fields'])
            return True
            
        except Exception as e:
            print(f"Error fixing comb fields: {e}")
            return False
            
    def _configure_acroform(self, acroform: DictionaryObject):
        """Configure AcroForm for better GhostScript compatibility"""
        try:
            # Set NeedAppearances to False - forces PDF to have proper appearances
            acroform[NameObject('/NeedAppearances')] = BooleanObject(False)
            
            # Remove XFA if present (can interfere with flattening)
            if '/XFA' in acroform:
                print("Removing XFA for better compatibility")
                del acroform['/XFA']
                
            print("Configured AcroForm for better GhostScript compatibility")
        except Exception as e:
            print(f"Error configuring AcroForm: {e}")
            
    def _process_fields_recursive(self, fields):
        """Process fields recursively to fix comb field issues"""
        for i, field_ref in enumerate(fields):
            try:
                field = field_ref.get_object()
                field_type = field.get('/FT', '')
                field_name = field.get('/T', '')
                
                # Process text fields
                if field_type == '/Tx':
                    self._fix_text_field(field, field_name)
                    
                # Process child fields if they exist
                if '/Kids' in field:
                    self._process_fields_recursive(field['/Kids'])
                    
            except Exception as e:
                print(f"Error processing field: {e}")
                
    def _fix_text_field(self, field: DictionaryObject, field_name: str):
        """Fix text field for better GhostScript compatibility"""
        try:
            # Check if it's a comb field
            is_comb_field = False
            if '/Ff' in field:
                flags = int(field['/Ff'])
                is_comb_field = bool(flags & (1 << 24))  # Comb flag is bit 24
                
            # Get current value
            value = field.get('/V', '')
            if not value:
                return  # Skip empty fields
                
            print(f"Processing field: {field_name}, value: {value}, comb: {is_comb_field}")
            
            # Generate robust appearance stream
            self._generate_robust_appearance(field, field_name, str(value), is_comb_field)
            
            # For comb fields, ensure proper DA string with explicit Tc
            if is_comb_field and '/DA' in field:
                self._optimize_comb_field_da(field, field_name, str(value))
                
        except Exception as e:
            print(f"Error fixing text field {field_name}: {e}")
            
    def _optimize_comb_field_da(self, field: DictionaryObject, field_name: str, value: str):
        """Optimize DA string for comb fields"""
        try:
            # Get current DA string
            current_da = str(field['/DA'])
            
            # Extract font size from DA string
            font_size = 0
            font_match = re.search(r'(\d+\.?\d*)\s+Tf', current_da)
            if font_match:
                font_size = float(font_match.group(1))
            
            # Get field dimensions
            if '/Rect' in field:
                rect = field['/Rect']
                field_width = rect[2] - rect[0]
                
                # Get max length
                max_len = field.get('/MaxLen', len(value))
                
                if max_len > 0 and field_width > 0:
                    # Calculate optimal character spacing
                    cell_width = field_width / max_len
                    char_width = font_size * 0.6  # Estimate character width
                    tc_value = cell_width - char_width
                    tc_value = max(0.0, tc_value)
                    
                    # Update DA string with explicit Tc
                    if ' Tc' in current_da:
                        # Replace existing Tc value
                        new_da = re.sub(r'(\d+\.?\d*)\s+Tc', f'{tc_value:.4f} Tc', current_da)
                    else:
                        # Add Tc to the end of DA string
                        new_da = f"{current_da} {tc_value:.4f} Tc"
                        
                    field[NameObject('/DA')] = TextStringObject(new_da)
                    print(f"Updated DA string for comb field '{field_name}': '{new_da}'")
        except Exception as e:
            print(f"Error optimizing DA for comb field {field_name}: {e}")
            
    def _generate_robust_appearance(self, field: DictionaryObject, field_name: str, value: str, is_comb: bool):
        """Generate robust appearance stream for better GhostScript compatibility"""
        try:
            # Clear existing appearance to force regeneration
            if '/AP' in field:
                field[NameObject('/AP')] = DictionaryObject()
                
            # Ensure proper quadding (alignment)
            if '/Q' not in field:
                field[NameObject('/Q')] = 1 if is_comb else 0  # Center for comb, left for others
                
            # For comb fields, ensure MaxLen is set
            if is_comb and '/MaxLen' not in field:
                field[NameObject('/MaxLen')] = len(value)
                
            # Set flags to ensure appearance generation
            if '/Ff' in field:
                flags = int(field['/Ff'])
                # Remove ReadOnly flag if present
                if flags & 1:
                    new_flags = flags & ~1
                    field[NameObject('/Ff')] = new_flags
                    
            print(f"Prepared robust appearance for field '{field_name}'")
        except Exception as e:
            print(f"Error generating appearance for field {field_name}: {e}")
            
    def save(self, output_path: str) -> bool:
        """Save the fixed PDF"""
        try:
            with open(output_path, 'wb') as output_file:
                self.writer.write(output_file)
            print(f"Successfully saved fixed PDF to: {output_path}")
            return True
        except Exception as e:
            print(f"Error saving PDF: {e}")
            return False

def main():
    """Main function to process PDF files"""
    if len(sys.argv) < 3:
        print("Usage: python comb_compatibility.py input.pdf output.pdf")
        return
        
    input_pdf = sys.argv[1]
    output_pdf = sys.argv[2]
    
    print(f"PDF Comb Field GhostScript Compatibility Fixer")
    print(f"Input PDF: {input_pdf}")
    print(f"Output PDF: {output_pdf}")
    print("-" * 70)
    
    fixer = CombFieldFixer(input_pdf)
    if fixer.fix_comb_fields():
        fixer.save(output_pdf)
        print(f"PDF fixed for GhostScript compatibility. Now try:")
        print(f"gs -o flattened.pdf -sDEVICE=pdfwrite {output_pdf}")
    else:
        print("Failed to fix PDF comb fields")

if __name__ == "__main__":
    main()