#!/usr/bin/env python3
"""
PDF Comb Field Compatibility Script for GhostScript Flattening
Fixes issues with numberOfCells fields losing content when flattened with GhostScript 9.27

Updated for PyPDF 5.0.0+ with enhanced error handling, compression, and robustness features.
"""

import sys
import re
from pathlib import Path
from typing import Optional, Union
import pypdf
from pypdf import PdfWriter, PdfReader
from pypdf.generic import (
    DictionaryObject, ArrayObject, IndirectObject, TextStringObject,
    NameObject, BooleanObject, NumberObject
)
from pypdf.errors import PdfReadError
import logging

# Configure logging for better debugging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

class CombFieldFixer:
    """
    Enhanced PDF Comb Field Fixer for PyPDF 5.0.0+

    Provides improved error handling, compression, and robustness features
    for fixing comb fields in PDF forms for better GhostScript compatibility.
    """

    def __init__(self, input_pdf: Union[str, Path]):
        """Initialize with input PDF path"""
        self.input_path = Path(input_pdf)
        self.reader: Optional[PdfReader] = None
        self.writer: Optional[PdfWriter] = None
        self.logger = logging.getLogger(__name__)

        # Validate input file
        if not self.input_path.exists():
            raise FileNotFoundError(f"PDF file not found: {self.input_path}")

        self.open_pdf()

    def open_pdf(self) -> bool:
        """Open PDF file and prepare for processing with enhanced error handling"""
        try:
            # Use context manager support introduced in PyPDF 5.0.0
            self.reader = PdfReader(str(self.input_path))

            # Enhanced error handling for encrypted PDFs
            if self.reader.is_encrypted:
                self.logger.warning("PDF is encrypted. Attempting to decrypt...")
                try:
                    if not self.reader.decrypt(""):
                        raise PdfReadError("PDF requires a password for modification")
                    self.logger.info("Successfully decrypted PDF with empty password")
                except Exception as e:
                    raise PdfReadError(f"Failed to decrypt PDF: {e}")

            # Initialize writer with compression enabled (PyPDF 5.0.0 feature)
            self.writer = PdfWriter(compress_identical_objects=True)

            # Clone the entire document structure
            try:
                self.writer.clone_reader_document_root(self.reader)
                self.logger.info("Successfully cloned PDF document structure")
            except Exception as e:
                self.logger.error(f"Failed to clone document: {e}")
                # Fallback to manual page copying
                self._manual_copy_fallback()

            return True

        except PdfReadError as e:
            self.logger.error(f"PDF read error: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error opening PDF: {e}")
            return False

    def _manual_copy_fallback(self) -> None:
        """Fallback method for manual PDF copying when clone fails"""
        self.logger.info("Using manual copy fallback method")

        # Copy all pages
        for page in self.reader.pages:
            self.writer.add_page(page)

        # Copy essential document catalog entries
        if '/Root' in self.reader.trailer:
            root = self.reader.trailer['/Root']
            essential_keys = ['/AcroForm', '/Names', '/Dests', '/ViewerPreferences']

            for key in essential_keys:
                if key in root:
                    self.writer._root_object[NameObject(key)] = root[key]
                    self.logger.debug(f"Copied {key} to writer")

    def fix_comb_fields(self) -> bool:
        """
        Fix comb fields for better GhostScript compatibility

        Enhanced for PyPDF 5.0.0 with improved error handling and robustness.
        """
        try:
            if not self.writer or not self.writer._root_object:
                raise PdfReadError("Writer not properly initialized")

            if '/AcroForm' not in self.writer._root_object:
                self.logger.warning("No AcroForm found in PDF - no form fields to fix")
                return False

            acroform = self.writer._root_object['/AcroForm']
            if '/Fields' not in acroform:
                self.logger.warning("No fields found in AcroForm")
                return False

            self.logger.info("Starting comb field fixes...")

            # Configure AcroForm for better flattening
            self._configure_acroform(acroform)

            # Process all fields with enhanced error handling
            fields_processed = self._process_fields_recursive(acroform['/Fields'])

            if fields_processed > 0:
                self.logger.info(f"Successfully processed {fields_processed} fields")
                return True
            else:
                self.logger.warning("No fields were processed")
                return False

        except PdfReadError as e:
            self.logger.error(f"PDF read error while fixing comb fields: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error fixing comb fields: {e}")
            return False
            
    def _configure_acroform(self, acroform: DictionaryObject) -> None:
        """
        Configure AcroForm for better GhostScript compatibility

        Enhanced for PyPDF 5.0.0 with improved robustness and error handling.
        """
        try:
            # Set NeedAppearances to False - forces PDF to have proper appearances
            # This is crucial for GhostScript compatibility
            acroform[NameObject('/NeedAppearances')] = BooleanObject(False)
            self.logger.info("Set NeedAppearances to False for better flattening")

            # Remove XFA if present (can interfere with flattening)
            if '/XFA' in acroform:
                self.logger.info("Removing XFA for better GhostScript compatibility")
                del acroform['/XFA']

            # Ensure proper form flags are set
            if '/Flags' not in acroform:
                # Set default flags for better compatibility
                acroform[NameObject('/Flags')] = NumberObject(0)
                self.logger.debug("Added default Flags to AcroForm")

            # Ensure proper DR (Default Resources) if missing
            if '/DR' not in acroform:
                self.logger.debug("AcroForm missing default resources")
                # We'll let the PDF viewer handle this

            self.logger.info("AcroForm configured for optimal GhostScript compatibility")

        except Exception as e:
            self.logger.error(f"Error configuring AcroForm: {e}")
            # Don't raise - continue with field processing
            
    def _process_fields_recursive(self, fields) -> int:
        """
        Process fields recursively to fix comb field issues

        Enhanced for PyPDF 5.0.0 with better error handling and field counting.

        Returns:
            int: Number of fields successfully processed
        """
        fields_processed = 0

        if not fields:
            return fields_processed

        # Handle both direct arrays and indirect object references
        if isinstance(fields, IndirectObject):
            fields = fields.get_object()

        if not isinstance(fields, (list, ArrayObject)):
            self.logger.warning(f"Expected array of fields, got {type(fields)}")
            return fields_processed

        for i, field_ref in enumerate(fields):
            try:
                # Safely resolve field reference
                if isinstance(field_ref, IndirectObject):
                    field = field_ref.get_object()
                else:
                    field = field_ref

                if not isinstance(field, DictionaryObject):
                    self.logger.warning(f"Field {i} is not a dictionary object")
                    continue

                field_type = field.get('/FT', '')
                field_name = field.get('/T', f'Field_{i}')

                # Convert field name to string if it's a text object
                if hasattr(field_name, 'get_original_bytes'):
                    field_name = str(field_name)

                self.logger.debug(f"Processing field: {field_name} (type: {field_type})")

                # Process text fields (where comb fields are found)
                if field_type == '/Tx':
                    if self._fix_text_field(field, field_name):
                        fields_processed += 1
                elif field_type:
                    # Other field types - just count them
                    fields_processed += 1
                    self.logger.debug(f"Processed non-text field: {field_name}")

                # Process child fields if they exist
                if '/Kids' in field:
                    child_count = self._process_fields_recursive(field['/Kids'])
                    fields_processed += child_count

            except Exception as e:
                self.logger.error(f"Error processing field {i}: {e}")
                # Continue processing other fields
                continue

        return fields_processed
                
    def _fix_text_field(self, field: DictionaryObject, field_name: str) -> bool:
        """
        Fix text field for better GhostScript compatibility

        Enhanced for PyPDF 5.0.0 with improved robustness and error handling.

        Returns:
            bool: True if field was successfully processed
        """
        try:
            # Check if it's a comb field
            is_comb_field = False
            field_flags = 0

            if '/Ff' in field:
                try:
                    field_flags = int(field['/Ff'])
                    is_comb_field = bool(field_flags & (1 << 24))  # Comb flag is bit 24
                except (ValueError, TypeError) as e:
                    self.logger.warning(f"Invalid field flags in {field_name}: {e}")

            # Get current value with better handling
            value = field.get('/V', '')

            # Handle different value types
            if hasattr(value, 'get_original_bytes'):
                # TextStringObject
                value = str(value)
            elif isinstance(value, (bytes, bytearray)):
                try:
                    value = value.decode('utf-8', errors='replace')
                except Exception:
                    value = str(value)
            else:
                value = str(value) if value else ''

            # Skip empty fields unless they're comb fields (which might need setup)
            if not value and not is_comb_field:
                self.logger.debug(f"Skipping empty non-comb field: {field_name}")
                return True

            self.logger.info(f"Processing field: {field_name}, value: '{value}', comb: {is_comb_field}")

            # Generate robust appearance stream
            self._generate_robust_appearance(field, field_name, value, is_comb_field)

            # For comb fields, ensure proper DA string with explicit Tc
            if is_comb_field and '/DA' in field:
                self._optimize_comb_field_da(field, field_name, value)

            return True

        except Exception as e:
            self.logger.error(f"Error fixing text field {field_name}: {e}")
            return False
            
    def _optimize_comb_field_da(self, field: DictionaryObject, field_name: str, value: str) -> None:
        """
        Optimize DA (Default Appearance) string for comb fields

        Enhanced for PyPDF 5.0.0 with better error handling and validation.
        """
        try:
            # Get current DA string
            da_obj = field.get('/DA')
            if not da_obj:
                self.logger.warning(f"No DA string found for comb field {field_name}")
                return

            current_da = str(da_obj)
            if not current_da.strip():
                self.logger.warning(f"Empty DA string for comb field {field_name}")
                return

            # Extract font size from DA string with improved regex
            font_size = 12.0  # Default font size
            font_match = re.search(r'(\d+(?:\.\d+)?)\s+Tf', current_da)
            if font_match:
                try:
                    font_size = float(font_match.group(1))
                except ValueError:
                    self.logger.warning(f"Invalid font size in DA string for {field_name}")
            else:
                self.logger.debug(f"No font size found in DA string for {field_name}, using default")

            # Get field dimensions with validation
            if '/Rect' not in field:
                self.logger.warning(f"No Rect found for comb field {field_name}")
                return

            rect = field['/Rect']
            if not isinstance(rect, (list, ArrayObject)) or len(rect) < 4:
                self.logger.warning(f"Invalid Rect for comb field {field_name}")
                return

            try:
                field_width = float(rect[2]) - float(rect[0])
                # field_height = float(rect[3]) - float(rect[1])  # Not used currently
            except (ValueError, TypeError, IndexError):
                self.logger.warning(f"Invalid Rect values for comb field {field_name}")
                return

            if field_width <= 0:
                self.logger.warning(f"Invalid field width for comb field {field_name}")
                return

            # Get max length with validation
            max_len = field.get('/MaxLen', len(value) if value else 1)
            try:
                max_len = int(max_len)
                if max_len <= 0:
                    max_len = len(value) if value else 1
            except (ValueError, TypeError):
                max_len = len(value) if value else 1

            # Calculate optimal character spacing
            cell_width = field_width / max_len
            char_width = font_size * 0.6  # Estimate character width (adjust based on font)
            tc_value = max(0.0, cell_width - char_width)

            # Update DA string with explicit Tc
            if re.search(r'\d+(?:\.\d+)?\s+Tc', current_da):
                # Replace existing Tc value
                new_da = re.sub(r'(\d+(?:\.\d+)?)\s+Tc', f'{tc_value:.4f} Tc', current_da)
            else:
                # Add Tc to the end of DA string
                new_da = f"{current_da.rstrip()} {tc_value:.4f} Tc"

            field[NameObject('/DA')] = TextStringObject(new_da)
            self.logger.info(f"Updated DA string for comb field '{field_name}': Tc={tc_value:.4f}")
            self.logger.debug(f"Full DA string: '{new_da}'")

        except Exception as e:
            self.logger.error(f"Error optimizing DA for comb field {field_name}: {e}")
            # Don't raise - continue processing
            
    def _generate_robust_appearance(self, field: DictionaryObject, field_name: str, value: str, is_comb: bool) -> None:
        """
        Generate robust appearance stream for better GhostScript compatibility

        Enhanced for PyPDF 5.0.0 with improved validation and error handling.
        """
        try:
            # Clear existing appearance to force regeneration
            # This ensures GhostScript will use the field's DA string properly
            if '/AP' in field:
                field[NameObject('/AP')] = DictionaryObject()
                self.logger.debug(f"Cleared existing appearance for field '{field_name}'")

            # Ensure proper quadding (text alignment)
            if '/Q' not in field:
                # 0 = left, 1 = center, 2 = right
                alignment = 1 if is_comb else 0  # Center for comb fields, left for others
                field[NameObject('/Q')] = NumberObject(alignment)
                self.logger.debug(f"Set text alignment for field '{field_name}': {alignment}")

            # For comb fields, ensure MaxLen is properly set
            if is_comb:
                current_max_len = field.get('/MaxLen')
                if not current_max_len or (value and len(value) > int(current_max_len)):
                    new_max_len = max(len(value), 1) if value else 1
                    field[NameObject('/MaxLen')] = NumberObject(new_max_len)
                    self.logger.debug(f"Set MaxLen for comb field '{field_name}': {new_max_len}")

            # Optimize field flags for better compatibility
            if '/Ff' in field:
                try:
                    flags = int(field['/Ff'])
                    original_flags = flags

                    # Remove ReadOnly flag (bit 0) if present to allow appearance generation
                    if flags & 1:
                        flags &= ~1
                        self.logger.debug(f"Removed ReadOnly flag from field '{field_name}'")

                    # Ensure proper comb field flags
                    if is_comb:
                        # Ensure comb flag (bit 24) is set
                        flags |= (1 << 24)
                        # Ensure the field doesn't have multiline flag (bit 12)
                        flags &= ~(1 << 12)

                    if flags != original_flags:
                        field[NameObject('/Ff')] = NumberObject(flags)
                        self.logger.debug(f"Updated flags for field '{field_name}': {original_flags} -> {flags}")

                except (ValueError, TypeError) as e:
                    self.logger.warning(f"Invalid field flags for {field_name}: {e}")

            # Ensure the field has a proper border for visibility
            if '/BS' not in field and '/Border' not in field:
                # Add a minimal border to ensure field visibility
                field[NameObject('/Border')] = ArrayObject([
                    NumberObject(0), NumberObject(0), NumberObject(1)  # [h_radius, v_radius, width]
                ])
                self.logger.debug(f"Added default border to field '{field_name}'")

            self.logger.info(f"Prepared robust appearance for field '{field_name}'")

        except Exception as e:
            self.logger.error(f"Error generating appearance for field {field_name}: {e}")
            # Don't raise - continue processing
            
    def save(self, output_path: Union[str, Path]) -> bool:
        """
        Save the fixed PDF with enhanced error handling

        Enhanced for PyPDF 5.0.0 with better compression and error reporting.
        """
        if not self.writer:
            self.logger.error("No writer available for saving")
            return False

        output_path = Path(output_path)

        try:
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Save with compression enabled
            with open(output_path, 'wb') as output_file:
                self.writer.write(output_file)

            # Verify the file was written
            if output_path.exists() and output_path.stat().st_size > 0:
                self.logger.info(f"Successfully saved fixed PDF to: {output_path}")
                self.logger.info(f"Output file size: {output_path.stat().st_size:,} bytes")
                return True
            else:
                self.logger.error("Output file was not created or is empty")
                return False

        except PermissionError as e:
            self.logger.error(f"Permission denied writing to {output_path}: {e}")
            return False
        except OSError as e:
            self.logger.error(f"OS error saving PDF to {output_path}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error saving PDF: {e}")
            return False

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        """Context manager exit with cleanup"""
        self.close()
        # Return None to propagate any exceptions

    def close(self) -> None:
        """Clean up resources"""
        if hasattr(self, 'reader') and self.reader:
            # PyPDF 5.0.0 supports context managers
            if hasattr(self.reader, 'stream') and hasattr(self.reader.stream, 'close'):
                try:
                    self.reader.stream.close()
                except Exception as e:
                    self.logger.debug(f"Error closing reader stream: {e}")

        self.reader = None
        self.writer = None
        self.logger.debug("Resources cleaned up")

def main():
    """
    Main function to process PDF files

    Enhanced for PyPDF 5.0.0 with better error handling and logging.
    """
    import argparse

    # Set up argument parser
    parser = argparse.ArgumentParser(
        description="PDF Comb Field GhostScript Compatibility Fixer (PyPDF 5.0.0+)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python comb_compatibility.py input.pdf output.pdf
  python comb_compatibility.py --verbose input.pdf output.pdf
  python comb_compatibility.py --quiet input.pdf output.pdf

This tool fixes comb field issues in PDF forms to improve compatibility
with GhostScript flattening operations.
        """
    )

    parser.add_argument('input_pdf', help='Input PDF file path')
    parser.add_argument('output_pdf', help='Output PDF file path')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--quiet', '-q', action='store_true',
                       help='Suppress all output except errors')
    parser.add_argument('--version', action='version',
                       version=f'PyPDF Comb Field Fixer (PyPDF {pypdf.__version__})')

    args = parser.parse_args()

    # Configure logging based on arguments
    if args.quiet:
        logging.getLogger().setLevel(logging.ERROR)
    elif args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    else:
        logging.getLogger().setLevel(logging.INFO)

    logger = logging.getLogger(__name__)

    logger.info("PDF Comb Field GhostScript Compatibility Fixer")
    logger.info(f"PyPDF Version: {pypdf.__version__}")
    logger.info(f"Input PDF: {args.input_pdf}")
    logger.info(f"Output PDF: {args.output_pdf}")
    logger.info("-" * 70)

    try:
        # Use context manager for automatic cleanup
        with CombFieldFixer(args.input_pdf) as fixer:
            if fixer.fix_comb_fields():
                if fixer.save(args.output_pdf):
                    logger.info("✓ PDF successfully fixed for GhostScript compatibility")
                    logger.info("Now you can flatten the PDF with:")
                    logger.info(f"  gs -o flattened.pdf -sDEVICE=pdfwrite {args.output_pdf}")
                    return 0
                else:
                    logger.error("✗ Failed to save the fixed PDF")
                    return 1
            else:
                logger.error("✗ Failed to fix PDF comb fields")
                return 1

    except FileNotFoundError as e:
        logger.error(f"✗ File not found: {e}")
        return 1
    except PdfReadError as e:
        logger.error(f"✗ PDF read error: {e}")
        return 1
    except Exception as e:
        logger.error(f"✗ Unexpected error: {e}")
        if args.verbose:
            import traceback
            logger.debug(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())